# Trading Volume Accumulation System Update

## Overview

This document describes the update to the trading volume accumulation system for Activity Cashback. The system has been changed from real-time NATS-based accumulation to scheduled batch processing via the Level Upgrade Task.

## Previous System (NATS-based)

### How it worked:
1. **NATS Events**: Trading events were processed via NATS messages
2. **Real-time Updates**: Each trade event immediately updated `user_tier_info.trading_volume_usd`
3. **Direct Accumulation**: Volume was added directly to the user's accumulated total via `AddTradingVolume()` method

### Issues:
- **Inconsistent Data**: NATS events might be missed or processed out of order
- **Performance Impact**: Real-time updates could cause database contention
- **Data Integrity**: Risk of duplicate processing or missed events
- **Debugging Difficulty**: Hard to trace volume accumulation issues

## New System (Scheduled Batch Processing)

### How it works:
1. **Daily Aggregation**: Level Upgrade Task runs every 10 minutes and aggregates yesterday's trading data
2. **Source of Truth**: `daily_meme_volumes` table contains daily MEME trading volumes per user
3. **Batch Calculation**: SUM of all-time volumes is calculated from `daily_meme_volumes`
4. **Scheduled Update**: `user_tier_info.trading_volume_usd` is updated with the calculated total

### Benefits:
- **Data Consistency**: Single source of truth from aggregated daily data
- **Better Performance**: Batch processing reduces database load
- **Easier Debugging**: Clear audit trail in `daily_meme_volumes` table
- **Reliability**: Less prone to missed events or duplicate processing

## Technical Changes

### 1. Level Upgrade Task Updates

**File**: `internal/task/level/level_upgrade.go`

**New Methods Added**:
```go
// updateAccumulatedTradingVolumes calculates and updates all-time accumulated trading volume
func (t *LevelUpgradeTask) updateAccumulatedTradingVolumes() error

// updateUserTierInfoTradingVolume updates or creates user_tier_info record
func (t *LevelUpgradeTask) updateUserTierInfoTradingVolume(userID uuid.UUID, totalVolume decimal.Decimal) error
```

**Updated Method**:
```go
// aggregateTransactionData now also calls updateAccumulatedTradingVolumes()
func (t *LevelUpgradeTask) aggregateTransactionData() error
```

### 2. NATS Processing Updates

**File**: `internal/service/activity_cashback/task_processors.go`

**Changes**:
- Removed `AddTradingVolume()` call from `ProcessTradingEvent()`
- NATS events now only process tasks, not volume accumulation
- Added logging to indicate volume accumulation is handled by Level Upgrade Task

### 3. Test Updates

**File**: `internal/service/activity_cashback/trading_volume_accumulation_test.go`

**New Tests**:
- `TestTradingVolumeAccumulation_NewSystem`: Tests that NATS no longer accumulates volume
- `TestLevelUpgradeTaskVolumeAccumulation`: Tests Level Upgrade Task volume calculation

## Data Flow

### Old Flow:
```
Trading Event → NATS → ProcessTradingEvent() → AddTradingVolume() → user_tier_info.trading_volume_usd
```

### New Flow:
```
Trading Event → daily_meme_volumes (via Level Upgrade Task aggregation)
                      ↓
Level Upgrade Task → SUM(daily_meme_volumes) → user_tier_info.trading_volume_usd
```

## Configuration

The Level Upgrade Task runs based on the cron configuration:

**File**: `config.yaml`
```yaml
cron-tasks:
  - id: "level_upgrade"
    cron: "0 */10 * * * *"  # Every 10 minutes
```

## API Impact

### ActivityCashbackSummary API

**Endpoint**: `activityCashbackSummary`
**Field**: `accumulatedTradingVolumeUsd`

**Behavior**:
- Still returns the same data from `user_tier_info.trading_volume_usd`
- Data is now updated by Level Upgrade Task instead of NATS events
- May have slight delay (up to 10 minutes) for new trading volume to appear

## Monitoring and Debugging

### Logs to Monitor:
1. **Level Upgrade Task**: Look for "Starting accumulated trading volume update for Activity Cashback"
2. **Volume Calculation**: "Found users with MEME trading volume"
3. **Update Results**: "Accumulated trading volume update completed"

### Database Queries for Debugging:

**Check daily volumes for a user**:
```sql
SELECT date, meme_volume_usd 
FROM daily_meme_volumes 
WHERE user_id = 'user-uuid' 
ORDER BY date DESC;
```

**Check accumulated volume**:
```sql
SELECT user_id, trading_volume_usd, updated_at 
FROM user_tier_info 
WHERE user_id = 'user-uuid';
```

**Verify calculation**:
```sql
SELECT 
    dmv.user_id,
    SUM(dmv.meme_volume_usd) as calculated_total,
    uti.trading_volume_usd as stored_total
FROM daily_meme_volumes dmv
LEFT JOIN user_tier_info uti ON dmv.user_id = uti.user_id
WHERE dmv.user_id = 'user-uuid'
GROUP BY dmv.user_id, uti.trading_volume_usd;
```

## Migration Notes

### For Existing Data:
1. The Level Upgrade Task will automatically calculate and update accumulated volumes for all users
2. No manual migration is required
3. The first run will process all historical data from `daily_meme_volumes`

### For Testing:
1. Run the updated tests to verify the new system works correctly
2. Monitor logs during the first few Level Upgrade Task runs
3. Verify that `accumulatedTradingVolumeUsd` in API responses reflects the correct totals

## Rollback Plan

If issues arise, the system can be rolled back by:
1. Reverting the changes in `task_processors.go` to re-enable NATS volume accumulation
2. Commenting out the `updateAccumulatedTradingVolumes()` call in Level Upgrade Task
3. The old NATS-based system will resume real-time volume accumulation
