package level

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// LevelUpgradeVolumeTestSuite tests the Level Upgrade Task volume accumulation functionality
type LevelUpgradeVolumeTestSuite struct {
	suite.Suite
	ctx  context.Context
	task *LevelUpgradeTask
}

// SetupSuite initializes the test suite
func (suite *LevelUpgradeVolumeTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test configuration and database
	test.SetupTestWithDB(suite.T())

	// Initialize Level Upgrade Task
	suite.task = NewLevelUpgradeTask()
}

// TearDownSuite cleans up after all tests
func (suite *LevelUpgradeVolumeTestSuite) TearDownSuite() {
	test.TeardownTest()
}

// TestUpdateAccumulatedTradingVolumes tests the new volume accumulation method
func (suite *LevelUpgradeVolumeTestSuite) TestUpdateAccumulatedTradingVolumes() {
	// Create test users
	user1ID := uuid.New()
	user2ID := uuid.New()
	user3ID := uuid.New()

	// Insert test data into daily_meme_volumes
	testData := []struct {
		userID uuid.UUID
		date   time.Time
		volume decimal.Decimal
	}{
		// User 1: Multiple days
		{user1ID, time.Now().AddDate(0, 0, -3), decimal.NewFromFloat(100.50)},
		{user1ID, time.Now().AddDate(0, 0, -2), decimal.NewFromFloat(250.75)},
		{user1ID, time.Now().AddDate(0, 0, -1), decimal.NewFromFloat(50.25)},

		// User 2: Single day
		{user2ID, time.Now().AddDate(0, 0, -1), decimal.NewFromFloat(1000.00)},

		// User 3: Small volumes
		{user3ID, time.Now().AddDate(0, 0, -2), decimal.NewFromFloat(0.001)},
		{user3ID, time.Now().AddDate(0, 0, -1), decimal.NewFromFloat(0.002)},
	}

	// Insert test data
	for _, data := range testData {
		dailyMemeVolume := model.DailyMemeVolume{
			UserID:        data.userID,
			Date:          data.date,
			MemeVolumeUSD: data.volume,
		}
		err := global.GVA_DB.Create(&dailyMemeVolume).Error
		suite.NoError(err, "Should be able to create daily meme volume record")
	}

	// Run the volume accumulation update
	err := suite.task.updateAccumulatedTradingVolumes()
	suite.NoError(err, "updateAccumulatedTradingVolumes should succeed")

	// Verify results for each user
	expectedVolumes := map[uuid.UUID]decimal.Decimal{
		user1ID: decimal.NewFromFloat(401.50), // 100.50 + 250.75 + 50.25
		user2ID: decimal.NewFromFloat(1000.00),
		user3ID: decimal.NewFromFloat(0.003), // 0.001 + 0.002
	}

	for userID, expectedVolume := range expectedVolumes {
		// Check user_tier_info
		var userTierInfo model.UserTierInfo
		err := global.GVA_DB.Where("user_id = ?", userID).First(&userTierInfo).Error
		suite.NoError(err, "Should find user tier info for user %s", userID.String())

		suite.Equal(expectedVolume, userTierInfo.TradingVolumeUSD,
			"User %s should have correct accumulated volume", userID.String())

		suite.T().Logf("✅ User %s: Expected %s, Got %s",
			userID.String()[:8],
			expectedVolume.String(),
			userTierInfo.TradingVolumeUSD.String())
	}
}

// TestUpdateAccumulatedTradingVolumes_EmptyData tests with no data
func (suite *LevelUpgradeVolumeTestSuite) TestUpdateAccumulatedTradingVolumes_EmptyData() {
	// Run the volume accumulation update with no data
	err := suite.task.updateAccumulatedTradingVolumes()
	suite.NoError(err, "updateAccumulatedTradingVolumes should succeed even with no data")

	// Verify no user_tier_info records were created
	var count int64
	err = global.GVA_DB.Model(&model.UserTierInfo{}).Count(&count).Error
	suite.NoError(err)
	suite.Equal(int64(0), count, "No user tier info records should be created")
}

// TestUpdateUserTierInfoTradingVolume_NewUser tests creating new user tier info
func (suite *LevelUpgradeVolumeTestSuite) TestUpdateUserTierInfoTradingVolume_NewUser() {
	userID := uuid.New()
	volume := decimal.NewFromFloat(500.75)

	// Update user tier info (should create new record)
	err := suite.task.updateUserTierInfoTradingVolume(userID, volume)
	suite.NoError(err, "updateUserTierInfoTradingVolume should succeed for new user")

	// Verify the record was created
	var userTierInfo model.UserTierInfo
	err = global.GVA_DB.Where("user_id = ?", userID).First(&userTierInfo).Error
	suite.NoError(err, "Should find newly created user tier info")

	suite.Equal(userID, userTierInfo.UserID)
	suite.Equal(volume, userTierInfo.TradingVolumeUSD)
	suite.Equal(1, userTierInfo.CurrentTier, "Should have default tier 1")
	suite.Equal(0, userTierInfo.TotalPoints, "Should have 0 points initially")
}

// TestUpdateUserTierInfoTradingVolume_ExistingUser tests updating existing user tier info
func (suite *LevelUpgradeVolumeTestSuite) TestUpdateUserTierInfoTradingVolume_ExistingUser() {
	userID := uuid.New()
	initialVolume := decimal.NewFromFloat(100.00)
	updatedVolume := decimal.NewFromFloat(250.50)

	// Create initial user tier info
	initialTierInfo := model.UserTierInfo{
		UserID:                userID,
		CurrentTier:           2,
		TotalPoints:           1000,
		PointsThisMonth:       500,
		TradingVolumeUSD:      initialVolume,
		ActiveDaysThisMonth:   5,
		CumulativeCashbackUSD: decimal.NewFromFloat(10.0),
		ClaimableCashbackUSD:  decimal.NewFromFloat(5.0),
		ClaimedCashbackUSD:    decimal.NewFromFloat(5.0),
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}
	err := global.GVA_DB.Create(&initialTierInfo).Error
	suite.NoError(err, "Should create initial user tier info")

	// Update trading volume
	err = suite.task.updateUserTierInfoTradingVolume(userID, updatedVolume)
	suite.NoError(err, "updateUserTierInfoTradingVolume should succeed for existing user")

	// Verify only trading volume was updated
	var userTierInfo model.UserTierInfo
	err = global.GVA_DB.Where("user_id = ?", userID).First(&userTierInfo).Error
	suite.NoError(err, "Should find updated user tier info")

	// Trading volume should be updated
	suite.Equal(updatedVolume, userTierInfo.TradingVolumeUSD, "Trading volume should be updated")

	// Other fields should remain unchanged
	suite.Equal(2, userTierInfo.CurrentTier, "Current tier should remain unchanged")
	suite.Equal(1000, userTierInfo.TotalPoints, "Total points should remain unchanged")
	suite.Equal(500, userTierInfo.PointsThisMonth, "Points this month should remain unchanged")
	suite.Equal(5, userTierInfo.ActiveDaysThisMonth, "Active days should remain unchanged")
	suite.Equal(decimal.NewFromFloat(10.0), userTierInfo.CumulativeCashbackUSD, "Cumulative cashback should remain unchanged")
	suite.Equal(decimal.NewFromFloat(5.0), userTierInfo.ClaimableCashbackUSD, "Claimable cashback should remain unchanged")
	suite.Equal(decimal.NewFromFloat(5.0), userTierInfo.ClaimedCashbackUSD, "Claimed cashback should remain unchanged")
}

// Run the test suite
func TestLevelUpgradeVolumeTestSuite(t *testing.T) {
	suite.Run(t, new(LevelUpgradeVolumeTestSuite))
}
